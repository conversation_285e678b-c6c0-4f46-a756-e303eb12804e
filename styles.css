/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #1a202c;
    overflow-x: hidden;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

html {
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
}

/* Header and Navigation */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.navbar {
    padding: 1.2rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.6rem;
    font-weight: 800;
    background: linear-gradient(135deg, #2563eb, #dc2626);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-logo i {
    margin-right: 0.5rem;
    font-size: 2rem;
    background: linear-gradient(135deg, #2563eb, #dc2626);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-link {
    text-decoration: none;
    color: #374151;
    font-weight: 600;
    font-size: 1rem;
    position: relative;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: linear-gradient(135deg, #2563eb, #dc2626);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover {
    color: #2563eb;
    transform: translateY(-2px);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.nav-toggle:hover {
    background: rgba(37, 99, 235, 0.1);
}

.bar {
    width: 25px;
    height: 3px;
    background: linear-gradient(135deg, #2563eb, #dc2626);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section */
.hero {
    color: white;
    padding: 140px 0 100px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-bg-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.85) 0%, rgba(30, 41, 59, 0.75) 25%, rgba(37, 99, 235, 0.65) 75%, rgba(59, 130, 246, 0.55) 100%);
    z-index: 2;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.3;
    z-index: 3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 4;
}

.hero-content {
    text-align: center;
    margin-bottom: 5rem;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 2rem;
    line-height: 1.1;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.btn {
    padding: 16px 40px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    display: inline-block;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    border-color: #dc2626;
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #b91c1c, #991b1b);
    border-color: #b91c1c;
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(220, 38, 38, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.95);
    color: #2563eb;
    border-color: rgba(255, 255, 255, 0.95);
    transform: translateY(-4px);
    box-shadow: 0 12px 35px rgba(255, 255, 255, 0.2);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2.5rem;
    text-align: center;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.stat {
    padding: 2.5rem 2rem;
    background: rgba(255,255,255,0.15);
    border-radius: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b, #d97706);
}

.stat:hover {
    transform: translateY(-8px) scale(1.02);
    background: rgba(255,255,255,0.2);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.stat-number {
    display: block;
    font-size: 3.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.95;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Keyframe Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 5rem;
    position: relative;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -2rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #dc2626);
    border-radius: 2px;
}

.section-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #1f2937, #374151);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    line-height: 1.2;
}

.section-subtitle {
    font-size: 1.3rem;
    color: #6b7280;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
    font-weight: 400;
}

/* Services Section */
.services {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.services .container {
    position: relative;
    z-index: 2;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2.5rem;
}

.service-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    padding: 3rem 2.5rem;
    border-radius: 24px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    border: 1px solid rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #dc2626, #f59e0b);
    transform: scaleX(0);
    transition: transform 0.5s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 25px 60px rgba(0,0,0,0.15);
    background: rgba(255, 255, 255, 0.95);
}

.service-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    position: relative;
    transition: all 0.4s ease;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.service-icon::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #2563eb, #dc2626, #f59e0b);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.service-card:hover .service-icon::after {
    opacity: 1;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
}

.service-icon i {
    font-size: 2.5rem;
    color: white;
    transition: transform 0.4s ease;
}

.service-card:hover .service-icon i {
    transform: scale(1.1);
}

.service-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1.25rem;
    color: #1a202c;
    transition: color 0.3s ease;
}

.service-card:hover .service-title {
    color: #2563eb;
}

.service-description {
    color: #4a5568;
    margin-bottom: 2rem;
    line-height: 1.7;
    font-size: 1.05rem;
}

.service-price {
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.service-price::before {
    content: 'Starting at ';
    font-size: 0.9rem;
    font-weight: 500;
    color: #6b7280;
    position: absolute;
    top: -1.5rem;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
}

/* About Section */
.about {
    padding: 100px 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-description {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.feature i {
    color: #2563eb;
    font-size: 1.25rem;
    width: 24px;
}

.about-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-gallery {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 500px;
}

.main-image {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    transition: all 0.4s ease;
}

.main-image:hover {
    transform: scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.2);
}

.main-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.main-image:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.8), rgba(220, 38, 38, 0.6));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.main-image:hover .image-overlay {
    opacity: 1;
}

.overlay-content {
    text-align: center;
    color: white;
}

.overlay-content i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.overlay-content p {
    font-size: 1.25rem;
    font-weight: 600;
}

.small-images {
    display: flex;
    gap: 1rem;
}

.small-image {
    flex: 1;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.small-image:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.small-image img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.small-image:hover img {
    transform: scale(1.1);
}

/* Reviews Section */
.reviews {
    padding: 100px 0;
    background: #f9fafb;
}

.reviews-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.review-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 4rem 3rem;
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    text-align: center;
    display: none;
    border: 1px solid rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
}

.review-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b, #d97706);
}

.review-card.active {
    display: block;
    animation: fadeInScale 0.6s ease-out;
}

.review-avatar {
    margin-bottom: 2rem;
}

.review-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fbbf24;
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.3);
    transition: all 0.3s ease;
}

.review-card:hover .review-avatar img {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(251, 191, 36, 0.4);
}

.review-stars {
    margin-bottom: 2rem;
}

.review-stars i {
    color: #fbbf24;
    font-size: 1.5rem;
    margin: 0 3px;
    transition: transform 0.2s ease;
}

.review-stars i:hover {
    transform: scale(1.2);
}

.review-text {
    font-size: 1.4rem;
    font-style: italic;
    color: #374151;
    margin-bottom: 2.5rem;
    line-height: 1.7;
    position: relative;
}

.review-text::before {
    content: '"';
    font-size: 4rem;
    color: #e5e7eb;
    position: absolute;
    top: -1rem;
    left: -1rem;
    font-family: serif;
}

.review-text::after {
    content: '"';
    font-size: 4rem;
    color: #e5e7eb;
    position: absolute;
    bottom: -2rem;
    right: -1rem;
    font-family: serif;
}

.review-author strong {
    display: block;
    font-size: 1.3rem;
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.review-author span {
    color: #6b7280;
    font-size: 1.1rem;
    font-weight: 500;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.review-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin-top: 2rem;
}

.review-btn {
    background: #2563eb;
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.review-btn:hover {
    background: #1d4ed8;
    transform: scale(1.1);
}

.review-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #d1d5db;
    cursor: pointer;
    transition: background 0.3s ease;
}

.dot.active {
    background: #2563eb;
}

/* Contact Section */
.contact {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="contact-grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="%23e2e8f0" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23contact-grid)"/></svg>');
    opacity: 0.5;
}

.contact .container {
    position: relative;
    z-index: 2;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
    align-items: start;
}

.contact-item {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 3rem;
    transition: transform 0.3s ease;
}

.contact-item:hover {
    transform: translateX(10px);
}

.contact-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    transition: all 0.4s ease;
    position: relative;
}

.contact-icon::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #2563eb, #dc2626, #f59e0b);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.contact-item:hover .contact-icon::after {
    opacity: 1;
}

.contact-item:hover .contact-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 35px rgba(37, 99, 235, 0.4);
}

.contact-icon i {
    color: white;
    font-size: 1.5rem;
    transition: transform 0.4s ease;
}

.contact-item:hover .contact-icon i {
    transform: scale(1.1);
}

.contact-details h3 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #1a202c;
    transition: color 0.3s ease;
}

.contact-item:hover .contact-details h3 {
    color: #2563eb;
}

.contact-details p {
    color: #4a5568;
    line-height: 1.7;
    font-size: 1.05rem;
}

.contact-form {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    padding: 3rem;
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
}

.contact-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #dc2626, #f59e0b);
}

.form-group {
    margin-bottom: 2rem;
    position: relative;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1.05rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    font-family: 'Inter', sans-serif;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2563eb;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-form .btn {
    width: 100%;
    margin-top: 1rem;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: white;
    padding: 80px 0 30px;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="footer-gradient" cx="50%" cy="50%"><stop offset="0%" stop-color="%232563eb" stop-opacity="0.1"/><stop offset="100%" stop-color="%232563eb" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="150" fill="url(%23footer-gradient)"/><circle cx="800" cy="600" r="200" fill="url(%23footer-gradient)"/></svg>');
    opacity: 0.3;
}

.footer .container {
    position: relative;
    z-index: 2;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #2563eb, #dc2626);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-logo i {
    margin-right: 0.75rem;
    font-size: 2rem;
    background: linear-gradient(135deg, #2563eb, #dc2626);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-section h3 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #e2e8f0;
    position: relative;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #2563eb, #dc2626);
    border-radius: 1px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.75rem;
    transition: transform 0.2s ease;
}

.footer-section ul li:hover {
    transform: translateX(5px);
}

.footer-section a {
    color: #cbd5e1;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.05rem;
    position: relative;
}

.footer-section a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    bottom: -2px;
    left: 0;
    background: linear-gradient(90deg, #2563eb, #dc2626);
    transition: width 0.3s ease;
}

.footer-section a:hover {
    color: #2563eb;
}

.footer-section a:hover::after {
    width: 100%;
}

.footer-section p {
    color: #cbd5e1;
    margin-bottom: 0.75rem;
    line-height: 1.6;
    font-size: 1.05rem;
}

.footer-section i {
    margin-right: 0.75rem;
    color: #2563eb;
    font-size: 1.1rem;
}

.footer-bottom {
    border-top: 1px solid rgba(55, 65, 81, 0.5);
    padding-top: 2.5rem;
    text-align: center;
    color: #9ca3af;
    font-size: 1.05rem;
}

/* Loading Animation */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading.fade-out {
    opacity: 0;
    pointer-events: none;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 5px solid #e5e7eb;
    border-top: 5px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    position: relative;
    margin-bottom: 2rem;
}

.spinner::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 5px solid transparent;
    border-top: 5px solid #2563eb;
    border-radius: 50%;
    animation: spin 1.5s linear infinite reverse;
}

.loading::after {
    content: 'Loading Midwest Auto Care...';
    color: #374151;
    font-size: 1.2rem;
    font-weight: 600;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* Scroll Animations */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Floating Animation for Hero Elements */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.hero-stats .stat:nth-child(1) {
    animation: float 3s ease-in-out infinite;
}

.hero-stats .stat:nth-child(2) {
    animation: float 3s ease-in-out infinite 0.5s;
}

.hero-stats .stat:nth-child(3) {
    animation: float 3s ease-in-out infinite 1s;
}

/* Glowing Effect for Important Elements */
.glow {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
    animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
    from { box-shadow: 0 0 20px rgba(37, 99, 235, 0.3); }
    to { box-shadow: 0 0 30px rgba(37, 99, 235, 0.5); }
}

/* Mobile Navigation Animation */
.nav-toggle.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .hero-stats {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: 2rem;
    }

    .image-placeholder {
        width: 100%;
        max-width: 400px;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .btn {
        width: 100%;
        max-width: 280px;
    }

    .review-text {
        font-size: 1.125rem;
    }

    .contact-form {
        padding: 1.5rem;
    }
}
