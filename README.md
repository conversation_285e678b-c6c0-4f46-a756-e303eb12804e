# Midwest Auto Care - Static Website

A professional, responsive website mockup for Midwest Auto Care, an automotive repair shop. This static website showcases the business's services, customer testimonials, and contact information with modern design and interactive features.

## Features

### 🎨 Design & Layout
- **Modern, Professional Design**: Clean and trustworthy appearance suitable for an auto repair business
- **Responsive Layout**: Fully responsive design that works on desktop, tablet, and mobile devices
- **Professional Color Scheme**: Blue and red color palette conveying trust and reliability
- **Smooth Animations**: Subtle animations and transitions for enhanced user experience

### 🚗 Auto Shop Specific Content
- **Comprehensive Services**: Oil changes, tire rotation, brake service, engine diagnostics, transmission service, and A/C repair
- **Service Pricing**: Starting prices displayed for each service
- **Customer Reviews**: Rotating testimonials from satisfied customers
- **Business Information**: Hours, location, contact details, and emergency service availability
- **Professional Credentials**: ASE certified technicians, warranty information

### 🔧 Interactive Features
- **Mobile Navigation**: Hamburger menu for mobile devices
- **Smooth Scrolling**: Navigation links smoothly scroll to sections
- **Review Carousel**: Auto-rotating customer testimonials with manual controls
- **Contact Form**: Functional contact form with validation
- **Notification System**: Success/error messages for form submissions
- **Back to Top Button**: Convenient scroll-to-top functionality
- **Animated Counters**: Statistics animate when scrolled into view

### 📱 Technical Features
- **Semantic HTML5**: Proper HTML structure for accessibility and SEO
- **CSS Grid & Flexbox**: Modern layout techniques for responsive design
- **Intersection Observer**: Efficient scroll-based animations
- **Form Validation**: Client-side form validation with user feedback
- **Performance Optimized**: Lightweight code with efficient animations

## File Structure

```
midwest-auto-care/
├── index.html          # Main HTML file
├── styles.css          # CSS styling and responsive design
├── script.js           # JavaScript functionality
└── README.md           # This documentation file
```

## Sections

### 1. Header & Navigation
- Fixed header with company logo
- Responsive navigation menu
- Mobile hamburger menu

### 2. Hero Section
- Compelling headline and call-to-action
- Key statistics (years in business, customers served, emergency service)
- Primary action buttons

### 3. Services Section
- 6 main automotive services with icons
- Service descriptions and starting prices
- Hover effects on service cards

### 4. About Section
- Company background and values
- Professional credentials and warranties
- Placeholder for shop image

### 5. Customer Reviews
- 3 rotating customer testimonials
- 5-star ratings display
- Navigation controls and auto-advance

### 6. Contact Section
- Business hours and location
- Phone and email contact
- Contact form with service selection
- Form validation and success messages

### 7. Footer
- Quick links and service overview
- Contact information
- Copyright notice

## Services Offered

1. **Oil Changes** - Starting at $29.99
2. **Tire Services** - Rotation, balancing, alignment - Starting at $19.99
3. **Brake Service** - Inspection, pad replacement, rotor work - Starting at $89.99
4. **Engine Diagnostics** - Computer diagnostics - Starting at $99.99
5. **Transmission Service** - Fluid changes and repairs - Starting at $149.99
6. **A/C Service** - Repair and recharge - Starting at $79.99

## Customer Testimonials

The website includes three rotating customer reviews:
- Sarah Johnson (Local Business Owner) - 5 stars
- Mike Thompson (Teacher) - 5 stars  
- Jennifer Davis (Nurse) - 5 stars

## Contact Information

- **Address**: 1234 Main Street, Springfield, IL 62701
- **Phone**: (*************
- **Email**: <EMAIL>
- **Hours**: 
  - Mon-Fri: 7:00 AM - 6:00 PM
  - Sat: 8:00 AM - 4:00 PM
  - Sun: Closed

## Browser Compatibility

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Customization

To customize this website for a real auto shop:

1. **Replace placeholder content** with actual business information
2. **Add real images** of the shop, staff, and services
3. **Update contact information** with actual details
4. **Modify services and pricing** to match actual offerings
5. **Replace testimonials** with real customer reviews
6. **Add Google Maps integration** for location
7. **Connect contact form** to actual email service

## Dependencies

- **Font Awesome 6.0.0**: For icons
- **Google Fonts (Inter)**: For typography
- **No JavaScript frameworks**: Pure vanilla JavaScript for maximum compatibility

## Performance

- Lightweight CSS and JavaScript
- Optimized animations using CSS transforms
- Efficient scroll event handling
- Lazy loading ready for images
- Mobile-first responsive design

## Future Enhancements

Potential additions for a production website:
- Online appointment booking system
- Service history tracking for customers
- Live chat integration
- Blog section for auto care tips
- Photo gallery of completed work
- Integration with review platforms
- SEO optimization
- Analytics tracking
